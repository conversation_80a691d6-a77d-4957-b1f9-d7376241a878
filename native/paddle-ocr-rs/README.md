[简体中文](./docs/README_zh-Hans.md)

## paddle-ocr-rs

Use Rust to call Paddle OCR models via ONNX Runtime for image text recognition.

### Command Line Usage

The package includes a command line tool that allows you to use PaddleOCR without writing Rust code:

```bash
# Basic usage
paddle-ocr --image /path/to/image.jpg

# Specify custom model paths
paddle-ocr --image /path/to/image.jpg --det-model /path/to/det_model.onnx --cls-model /path/to/cls_model.onnx --rec-model /path/to/rec_model.onnx

# Enable angle detection
paddle-ocr --image /path/to/image.jpg --do-angle

# Use JSON output format
paddle-ocr --image /path/to/image.jpg --format json

# Force download models even if they exist
paddle-ocr --image /path/to/image.jpg --download-models

# Show full help
paddle-ocr --help
```

#### Automatic Model Downloads

The command line tool will automatically download required model files when they're not found. By default, it checks for models in the `./models` directory. You can force re-download of models using the `--download-models` flag.

#### Command Line Options

```
Options:
  -i, --image <IMAGE>                            Path to the input image
  -d, --det-model <DET_MODEL>                    Path to detection model [default: ./models/ch_PP-OCRv5_mobile_det.onnx]
  -c, --cls-model <CLS_MODEL>                    Path to classification model [default: ./models/ch_ppocr_mobile_v2.0_cls_infer.onnx]
  -r, --rec-model <REC_MODEL>                    Path to recognition model [default: ./models/ch_PP-OCRv5_rec_mobile_infer.onnx]
  -p, --padding <PADDING>                        Padding size [default: 50]
  -s, --max-side-len <MAX_SIDE_LEN>              Max side length for image resizing [default: 1024]
  -b, --box-score-thresh <BOX_SCORE_THRESH>      Box score threshold [default: 0.5]
  -t, --box-thresh <BOX_THRESH>                  Text detection threshold [default: 0.3]
  -u, --unclip-ratio <UNCLIP_RATIO>              Unclip ratio [default: 1.6]
  -a, --do-angle                                 Enable angle detection
  -m, --most-angle                               Use most angle
  -k, --angle-rollback-threshold <ANGLE_ROLLBACK_THRESHOLD>
                                                 Enable angle rollback and set threshold
  -j, --num-thread <NUM_THREAD>                  Number of threads for inference [default: 2]
  -f, --format <FORMAT>                          Output format [default: text] [possible values: text, json, debug]
  -D, --download-models                          Force model download even if files exist
  -h, --help                                     Print help
  -V, --version                                  Print version
```

### Static Linking

github: https://github.com/csukuangfj/onnxruntime-libs

./build.sh --config <Release|Debug|RelWithDebInfo|MinSizeRel> --use_xcode \
           --ios --apple_sysroot iphoneos --osx_arch arm64 --apple_deploy_target <minimal iOS version>
./build.sh --config Release --use_xcode --ios --apple_sysroot iphoneos --osx_arch arm64 --apple_deploy_target 14.0
