#![allow(unused)]
use crate::signals::*;
use anyhow::Result;
use futures::channel::oneshot;
use lazy_static::lazy_static;
use pdfium_render::prelude::PdfiumError;
use pdfium_render::prelude::*;
use regex::Regex;
use rinf::{DartSignal, RustSignal, debug_print};
use serde::{Deserialize, Serialize};
use serde_json::Value as JsonValue;
use serde_json::{Value, json};
use std::collections::HashMap;
use std::fmt::format;
use std::fs::{File, create_dir_all};
use std::io::Cursor;
use std::io::{self, Read, Write};
use std::path::{Path, PathBuf};
use std::process::Command;
use std::str::FromStr;
use std::sync::Mutex;
use std::thread;
use std::time::Duration;
use thiserror::Error;
use ulid::Ulid;

#[derive(Error, Debug)]
pub enum PdfError {
  #[error("PageRange Error: {0}")]
  PageRange(String),
  #[error("IO Error: {0}")]
  Io(#[from] std::io::Error),
  #[error("Other Error: {0}")]
  Other(String),
  #[error("Render Error: {0}")]
  Render(String),
  #[error("Pdfium Error: {0}")]
  Pdfium(#[from] PdfiumError),
}

// 全局响应通道存储
lazy_static! {
  pub static ref RESPONSE_CHANNELS: Mutex<HashMap<String, oneshot::Sender<DartResponse>>> =
    Mutex::new(HashMap::new());
}

pub async fn send_dart_request(
  data: Value,
  msg_type: String,
) -> Result<DartResponse> {
  let interaction_id = Ulid::new().to_string();
  let (sender, receiver) = oneshot::channel::<DartResponse>();

  // 存储响应通道
  RESPONSE_CHANNELS
    .lock()
    .unwrap()
    .insert(interaction_id.clone(), sender);

  // 构造请求消息
  let request = json!({
      "params": data.to_string(),
      "msgType": msg_type.clone(),
      "interactionId": interaction_id.clone()
  });

  // 发送到 Dart
  RustRequest {
    interaction_id: interaction_id.clone(),
    msg_type,
    params: request.to_string(),
  }
  .send_signal_to_dart();

  // 等待响应
  let response = receiver.await?;
  debug_print!("response: {:?}", response);
  Ok(response)
}

pub async fn get_temp_dir() -> Result<String> {
  // 发送请求到 Dart 端获取临时目录路径
  let response = send_dart_request(
    json!({}), // 空参数
    "get_temp_dir".to_string(),
  )
  .await?;
  debug_print!("response: {:?}", response);
  let status = response.status;
  let data = response.data;
  if status == "success" {
    Ok(data)
  } else {
    Err(anyhow::anyhow!("Failed to get temp directory path"))
  }
}

pub fn get_pdfium(libpath: Option<String>) -> Result<Pdfium, PdfError> {
  let path = if let Some(path) = libpath {
    path
  } else {
    #[cfg(target_os = "macos")]
    let path = "/Library/Application Support/PDF Guru Anki2/".to_string();
    #[cfg(target_os = "ios")]
    let path = "./pdfium.xcframework/".to_string();
    #[cfg(not(any(target_os = "macos", target_os = "ios")))]
    let path = "./".to_string();
    path
  };
  #[cfg(any(
    target_os = "windows",
    target_os = "macos",
    target_os = "linux",
    target_os = "android"
  ))]
  {
    let bindings = Pdfium::bind_to_library(
      Pdfium::pdfium_platform_library_name_at_path(&path),
    )
    .or_else(|_| Pdfium::bind_to_system_library())
    .map_err(PdfError::Pdfium)?;
    Ok(Pdfium::new(bindings))

    // 静态链接库
    // Ok(Pdfium::new(
    //     Pdfium::bind_to_statically_linked_library().unwrap(),
    // ))
  }
  #[cfg(target_os = "ios")]
  {
    // 静态链接
    Ok(Pdfium::new(
      Pdfium::bind_to_statically_linked_library().unwrap(),
    ))
  }
}

/// 获取PDF文件的页数
pub fn get_pdf_page_count(path: &str) -> Result<u32, PdfError> {
  let pdfium = get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;
  let document = pdfium
    .load_pdf_from_file(path, None)
    .map_err(|e| PdfError::Other(e.to_string()))?;
  let pages = document.pages();
  Ok(pages.len() as u32)
}
