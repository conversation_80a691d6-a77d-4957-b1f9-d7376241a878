#![allow(unused)]
#![allow(elided_lifetimes_in_paths)]
#![allow(invalid_reference_casting)]
mod signals;
use crate::signals::*;
use rinf::{DartSignal, RustSignal};
// use tokio_with_wasm::alias as tokio;
rinf::write_interface!();
use rinf::debug_print;
use std::collections::HashMap;
mod pdf_utils;
mod ocr;

/// Helper struct to handle response messages
#[derive(Clone, Debug)]
struct ResponseHandler {
  interaction_id: String,
}

impl ResponseHandler {
  fn new(interaction_id: String) -> Self {
    Self { interaction_id }
  }

  fn send_response(&self, status: &str, message: &str, data: String) {
    RustResponse {
      interaction_id: self.interaction_id.clone(),
      status: status.to_string(),
      message: message.to_string(),
      data,
    }
    .send_signal_to_dart();
  }
}

/// 创建一个进度回调函数
fn progress_callback(current: f64, total: f64, message: String) {
  ProgressResponse {
    status: "running".to_string(),
    message,
    current,
    total,
  }
  .send_signal_to_dart();
}

/// 创建一个空的进度回调函数（不执行任何操作）
fn no_op_progress_callback(_current: f64, _total: f64, _message: String) {
  // 不执行任何操作，用于禁用进度报告时
}

/// 根据show_progress参数创建条件进度回调函数
fn create_conditional_progress_callback(
  show_progress: bool,
) -> impl Fn(f64, f64, String) + Clone + Send + Sync {
  if show_progress {
    progress_callback
  } else {
    no_op_progress_callback
  }
}

/// 根据show_progress参数创建条件进度回调函数
fn create_conditional_progress_callback_with_handler(
  show_progress: bool,
  _handler: ResponseHandler,
) -> impl Fn(f64, f64, String) + Clone + Send + Sync {
  move |current: f64, total: f64, message: String| {
    if show_progress {
      ProgressResponse {
        status: "running".to_string(),
        message,
        current,
        total,
      }
      .send_signal_to_dart();
    }
  }
}

pub async fn communicate() {
  let receiver = DartRequest::get_dart_signal_receiver(); // GENERATED
  while let Some(dart_signal) = receiver.recv().await {
    let message = dart_signal.message;
    let data = message.params;
    let msg_type = message.msg_type;
    let interaction_id = message.interaction_id;

    debug_print!("{interaction_id}");
    debug_print!("{data:?}");
    let data_str = data.as_str();
    let params: serde_json::Value = match serde_json::from_str(data_str) {
      Ok(json) => json,
      Err(e) => {
        debug_print!("JSON parsing error: {}", e);
        RustResponse {
          interaction_id: interaction_id.to_string(),
          status: "error".to_string(),
          message: format!("JSON parsing error: {e}").to_string(),
          data: "".to_string(),
        }
        .send_signal_to_dart();
        return;
      }
    };
    debug_print!("Parsed JSON: {:?}", params);
    let mut show_progress = params
      .get("show_progress")
      .and_then(|v| v.as_bool())
      .unwrap_or(true);
    let no_progress_types = vec!["verify_license", "open_file", "wbi_sign"];
    if no_progress_types.contains(&msg_type.as_str()) {
      show_progress = false;
    }
    if show_progress {
      ProgressResponse {
        status: "running".to_string(),
        message: "处理中...".to_string(),
        current: 1.,
        total: 100.,
      }
      .send_signal_to_dart();
    }
    match Some(msg_type.as_str()) {
      Some("test_pdfium") => {
        let handler = ResponseHandler::new(interaction_id.to_string());
        match pdf_utils::get_pdfium(None) {
          Ok(count) => {
            handler.send_response(
              "success",
              "test_pdfium passed",
              "true".to_string(),
            );
          }
          Err(e) => {
            debug_print!("test_pdfium error: {}", e);
            handler.send_response("error", &e.to_string(), "".to_string());
          }
        }
      }
      Some("get_pdf_page_count") => {
        let handler = ResponseHandler::new(interaction_id.to_string());
        let path = params["path"].as_str().unwrap_or("");
        match pdf_utils::get_pdf_page_count(path) {
          Ok(count) => {
            handler.send_response(
              "success",
              "get_pdf_page_count passed",
              count.to_string(),
            );
          }
          Err(e) => {
            debug_print!("get_pdf_page_count error: {}", e);
            handler.send_response("error", &e.to_string(), "".to_string());
          }
        }
      }
      Some("get_temp_dir") => {
        let handler = ResponseHandler::new(interaction_id.to_string());
        match pdf_utils::get_temp_dir().await {
          Ok(data) => {
            handler.send_response(
              "success",
              "get_temp_dir passed",
              data.to_string(),
            );
          }
          Err(e) => {
            debug_print!("get_temp_dir error: {}", e);
            handler.send_response("error", &e.to_string(), "".to_string());
          }
        }
      }
      none => {
        debug_print!("Missing operation type");
        let handler = ResponseHandler::new(interaction_id.to_string());
        handler.send_response(
          "error",
          "Unknown operation type",
          "".to_string(),
        );
      }
    }
  }
}

pub async fn communicate_dart() {
    let receiver = DartResponse::get_dart_signal_receiver(); // GENERATED
    while let Some(dart_signal) = receiver.recv().await {
        let response = dart_signal.message;
        let interaction_id = response.interaction_id.clone();
        debug_print!("response: {:?}", &response);
        // 使用 take() 来获取所有权
        if let Some(sender) = pdf_utils::RESPONSE_CHANNELS
            .lock()
            .unwrap()
            .remove(&interaction_id)
        {
            let _ = sender.send(response);
        }
    }
}

// You can go with any async library, not just `tokio`.
#[tokio::main(flavor = "current_thread")]
async fn main() {
  tokio::spawn(async {
    let task = tokio::spawn(communicate());
  });
  tokio::spawn(async {
    let task = tokio::spawn(communicate_dart());
  });
  // Keep the main function running until Dart shutdown.
  rinf::dart_shutdown().await;
}
