[package]
# Do not change the name of this crate.
name = "hub"
version = "0.1.0"
edition = "2024"

[lib]
# `lib` is required for non-library targets,
# such as tests and benchmarks.
# `cdylib` is for Linux, Android, Windows, and web.
# `staticlib` is for iOS and macOS.
crate-type = ["lib", "cdylib", "staticlib"]

[lints.clippy]
unwrap_used = "deny"
expect_used = "deny"
wildcard_imports = "deny"

[dependencies]
rinf = "8.7.1"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.45.0", features = ["rt-multi-thread", "macros", "sync"] }
tokio_with_wasm = { version = "0.8.5", features = [
    "rt",
    "sync",
    "time",
    "macros",
] }
wasm-bindgen = "0.2.100"
async-trait = "0.1.87"
messages = "0.3.1"
anyhow = "1.0.89"
sample_crate = { path = "../sample_crate" }
serde_json = "1.0.133"
futures-util = "0.3.31"
thiserror = "2.0.9"
chrono = "0.4.39"
once_cell = "1.20.2"
paddle-ocr-rs = { path = "../paddle-ocr-rs" }
lazy_static = "1.5.0"
futures = "0.3.31"
ulid = "1.2.1"
regex = "1.11.1"


[target.'cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))'.dependencies]
pdfium-render = { git = "https://github.com/ajrcarey/pdfium-render.git" }
killport = { git = "https://github.com/jkfran/killport.git" }

[target.'cfg(any(target_os = "android"))'.dependencies]
openssl = { version = "*", features = ["vendored"] }
pdfium-render = { git = "https://github.com/ajrcarey/pdfium-render.git" }

[target.'cfg(any(target_os = "ios"))'.dependencies]
openssl = { version = "*", features = ["vendored"] }
pdfium-render = { git = "https://github.com/ajrcarey/pdfium-render.git", features = [
    "image",
    "thread_safe",
    "pdfium_latest",
    "static",
    "libc++",
    "core_graphics",
] }
