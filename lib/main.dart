import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:rinf/rinf.dart';
import 'src/bindings/bindings.dart';
import 'message_controller.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';

Future<void> main() async {
  await initializeRust(assignRustSignal);
  final messageController = Get.put(MessageController());
  RustResponse.rustSignalStream.listen((rustSignal) {
    final message = rustSignal.message;
    messageController.messageChannel.value[message.interactionId]!.complete(
      message,
    );
  });
  RustRequest.rustSignalStream.listen((rustSignal) async {
    final message = rustSignal.message;
    debugPrint('RustRequest: $message');

    try {
      switch (message.msgType) {
        case "get_temp_dir":
          final dir = await getTemporaryDirectory();
          DartResponse(
            interactionId: message.interactionId,
            status: "success",
            message: "Success",
            data: dir.path,
          ).sendSignalToRust();
          break;
      }
    } catch (e, stackTrace) {
      debugPrint('Error handling RustRequest: $e\n$stackTrace');
    }
  });
  runApp(const DebugApp());
}

// Simple debug app to isolate splash screen issues
class DebugApp extends StatelessWidget {
  const DebugApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Debug App',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const DebugPage(),
    );
  }
}

class DebugPage extends StatefulWidget {
  const DebugPage({super.key});

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  int _counter = 0;
  String _statusMessage = 'App is running successfully!';
  bool _isLoading = false;

  void _testPdfium() async {
    setState(() {
      _statusMessage = '处理中...';
    });
    final messageController = Get.find<MessageController>();
    final resp = await messageController.request({
      "path": "test.pdf",
    }, "test_pdfium");
    setState(() {
      _statusMessage = 'resp: ${resp}';
    });
  }
  void _testGetPDFPageCount() async {
    setState(() {
      _statusMessage = '选择PDF文件...';
    });

    // 使用文件选择器选择PDF文件
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
      allowMultiple: false,
    );

    if (result != null && result.files.single.path != null) {
      setState(() {
        _statusMessage = '处理中...';
      });

      final messageController = Get.find<MessageController>();
      final resp = await messageController.request({
        "path": result.files.single.path!,
      }, "get_pdf_page_count");
      setState(() {
        _statusMessage = 'resp: ${resp}';
      });
    } else {
      setState(() {
        _statusMessage = '未选择文件';
      });
    }
  }

  void _testTempDir() async {
    setState(() {
      _statusMessage = '处理中...';
    });
    final messageController = Get.find<MessageController>();
    final resp = await messageController.request({}, "get_temp_dir");
    setState(() {
      _statusMessage = 'resp: ${resp}';
    });
  }

  void _incrementCounter() {
    setState(() {
      _counter++;
      _statusMessage = 'Button pressed $_counter times';
    });
  }

  void _simulateAsyncOperation() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Running async operation...';
    });

    // Simulate some async work
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
      _statusMessage = 'Async operation completed successfully!';
    });
  }

  void _navigateToSecondPage() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SecondDebugPage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('Debug Page'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.check_circle, color: Colors.green, size: 64),
              const SizedBox(height: 20),
              const Text(
                'Flutter App Debug Mode',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Text(
                _statusMessage,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Text(
                'Counter: $_counter',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 30),
              if (_isLoading)
                const CircularProgressIndicator()
              else
                Column(
                  children: [
                    ElevatedButton(
                      onPressed: _testPdfium,
                      child: const Text('test pdfium'),
                    ),
                    ElevatedButton(
                      onPressed: _testTempDir,
                      child: const Text('test temp_dir'),
                    ),
                    ElevatedButton(
                      onPressed: _testGetPDFPageCount,
                      child: const Text('test get_pdf_page_count'),
                    ),
                    ElevatedButton(
                      onPressed: _incrementCounter,
                      child: const Text('Increment Counter'),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _simulateAsyncOperation,
                      child: const Text('Test Async Operation'),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _navigateToSecondPage,
                      child: const Text('Navigate to Second Page'),
                    ),
                  ],
                ),
              const SizedBox(height: 30),
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Debug Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10),
                      Text('✓ Flutter framework loaded'),
                      Text('✓ Material Design components working'),
                      Text('✓ State management functional'),
                      Text('✓ Navigation system ready'),
                      Text('✓ Async operations supported'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ),
    );
  }
}

class SecondDebugPage extends StatelessWidget {
  const SecondDebugPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Second Debug Page'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.navigation, color: Colors.blue, size: 64),
              SizedBox(height: 20),
              Text(
                'Navigation Test Successful!',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text(
                'This page confirms that navigation between screens is working properly.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 30),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Navigation Test Results',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10),
                      Text('✓ Route navigation working'),
                      Text('✓ AppBar back button functional'),
                      Text('✓ Page transitions smooth'),
                      Text('✓ State preservation working'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
