import 'dart:async';
import 'src/bindings/bindings.dart';
import 'package:get/get.dart';
import 'package:ulid/ulid.dart';
import 'dart:convert';

class MessageController extends GetxController {
  final messageChannel = Rx<Map<String, Completer<RustResponse>>>({});

  Future<RustResponse> request(
    Map<String, dynamic> data,
    String msgType,
  ) async {
    final completer = Completer<RustResponse>();
    final params = DartRequest(
      params: jsonEncode(data),
      msgType: msgType,
      interactionId: Ulid().toUuid(),
    );
    messageChannel.value[params.interactionId] = completer;
    params.sendSignalToRust();
    final resp = await completer.future;
    messageChannel.value.remove(params.interactionId);
    return resp;
  }
}
